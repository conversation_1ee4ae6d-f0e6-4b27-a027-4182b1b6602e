import os
import json
from azure.cosmos import CosmosClient

# 🔹 Load and parse environment variables
COSMOS_CONNECTION_STRING = os.getenv("COSMOS_CONNECTION_STRING")
COSMOS_CONTAINER_NAME = os.getenv("COSMOS_CONTAINER_NAME")
COSMOS_DB_NAME = os.getenv("COSMOS_DB_NAME")
RULES_DOC_ID = "rules.json_0"  # The rules document ID in Cosmos DB

if not COSMOS_CONNECTION_STRING or not COSMOS_CONTAINER_NAME or not COSMOS_DB_NAME:
    raise EnvironmentError(
        "Missing Cosmos DB environment variables. "
        "Ensure COSMOS_CONNECTION_STRING, COSMOS_CONTAINER_NAME, and COSMOS_DB_NAME are set."
    )

# 🔹 Parse AccountEndpoint and AccountKey from the connection string
def parse_connection_string(conn_str):
    parts = dict(
        item.split("=", 1) for item in conn_str.strip(";").split(";") if "=" in item
    )
    return parts.get("AccountEndpoint"), parts.get("AccountKey")

COSMOS_ENDPOINT, COSMOS_KEY = parse_connection_string(COSMOS_CONNECTION_STRING)

# ───────────────────────────────
# ✅ Fetch RBAC rules dynamically
# ───────────────────────────────
def fetch_rules():
    client = CosmosClient(COSMOS_ENDPOINT, COSMOS_KEY)
    db = client.get_database_client(COSMOS_DB_NAME)
    container = db.get_container_client(COSMOS_CONTAINER_NAME)

    doc = container.read_item(item=RULES_DOC_ID, partition_key=RULES_DOC_ID)
    return doc.get("rbac_rules", [])

# ───────────────────────────────
# ✅ Evaluate request against rules
# ───────────────────────────────
def evaluate_request(email_payload: dict, role: str, tessa_id: str = None):
    """
    email_payload: dict → Email data in JSON format.
    role: str → Role selected from UI (e.g., "Manager", "Admin", "Employee").
    tessa_id: str|None → Optional TESSA ID entered in UI.
    """
    rules = fetch_rules()
    resource = email_payload.get("resource", "generic")

    for rule in rules:
        match = rule.get("match", {})

        # Require TESSA ID
        if match.get("tessa_id_required", False) and not tessa_id:
            return False, rule["safe_message"]

        # Role-based restriction
        if match.get("resource") == resource:
            min_clearance = match.get("min_clearance")
            allowed_roles = ["Manager", "Admin"]
            if min_clearance == "Manager" and role not in allowed_roles:
                return False, rule["safe_message"]

    return True, f"Access granted for role '{role}' to resource '{resource}'."

# ───────────────────────────────
# 🚀 Entry point to process email JSON
# ───────────────────────────────
def process_email(json_file_path, role_input, tessa_id_input=None):
    with open(json_file_path, "r") as f:
        email_data = json.load(f)

    allowed, message = evaluate_request(email_data, role_input, tessa_id_input)
    return message
